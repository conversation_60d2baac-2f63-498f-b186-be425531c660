package com.wantwant.sfa.backend.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午11:47
 */


@AllArgsConstructor
public enum TaskProcessStepEnum {
    PUSH(10,"发布审核"),
    AUDIT_PUSH(11,"老板审核"),
    SIGN(20,"签收"),
    PROCESSING(30,"进行中"),
    AUDIT(40,"送审"),
    FINAL_AUDIT(41,"老板审核"),
    MEETING_CONFIRM(42,"待会议确认"),
    ENDING_FINISH(43,"待确认完结");


    @Getter
    private final Integer processStep;

    @Getter
    private final String name;

}
