package com.wantwant.sfa.backend.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.task.*;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.task.dto.TaskAssignDTO;
import com.wantwant.sfa.backend.task.dto.TaskLogDTO;
import com.wantwant.sfa.backend.task.dto.TaskProcessDTO;
import com.wantwant.sfa.backend.task.entity.*;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskProcessStepEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import com.wantwant.sfa.backend.task.service.ITaskProcessService;
import com.wantwant.sfa.backend.taskManagement.request.LaunchMeetingRequest;
import com.wantwant.sfa.backend.taskManagement.request.ModifyDeadlineRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskAuditRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午11:44
 */
@Service
@Slf4j
public class TaskProcessService implements ITaskProcessService {

    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaTaskAssignMapper sfaTaskAssignMapper;
    @Autowired
    private SfaTaskInstanceRecordMapper sfaTaskInstanceRecordMapper;
    @Autowired
    private SfaTaskInstanceMapper sfaTaskInstanceMapper;
    @Autowired
    private ITaskLogService taskLogService;
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private NotifyService notifyService;
    @Resource
    private TaskMeetingMapper taskMeetingMapper;

    @Override
    @Transactional
    public void initProcess(Long taskId, Integer taskType, String createUserId, String createUserName) {
        log.info("【task process init】taskId:{},taskType:{}",taskId,taskType);

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);



        // 交办任务需要审核，个人任务不需要审核
        TaskProcessDTO taskProcessDTO = getProcessStep(sfaTaskEntity,createUserId);

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
        // 获取审核人信息
        int processStep = taskProcessDTO.getProcessStep();

        // 交办任务发布审核先到杜颖
        if(sfaTaskEntity.getTaskType() == 1){
            String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
            // 老板发布的任务直接到主办人签收
            if(createUserId.equals(zw_senior_hr_employee_id)){
                LambdaQueryWrapper<SfaTaskAssignEntity> sfaTaskAssignQueryWrapper = new LambdaQueryWrapper<>();
                sfaTaskAssignQueryWrapper.eq(SfaTaskAssignEntity::getTaskId,sfaTaskEntity.getTaskId())
                        .eq(SfaTaskAssignEntity::getDeleteFlag,0)
                        .eq(SfaTaskAssignEntity::getStatus,1)
                        .eq(SfaTaskAssignEntity::getAssignType,1);
                SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(sfaTaskAssignQueryWrapper);
                ceoBusinessOrganizationPositionRelation= ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq("employee_id", sfaTaskAssignEntity.getAssignUserId())
                        .eq("channel", 3)
                        .last("limit 1")
                );
                //老板提交直接进入主办人待签收状态
                sfaTaskEntity.setStatus(TaskStatusEnum.READY_SIGN.getStatus());
                sfaTaskEntity.setPublishTime(LocalDateTime.now());
                processStep = TaskProcessStepEnum.SIGN.getProcessStep();
            }else{
                String seniorEmpId = configMapper.getValueByCode("task_audit_person");
                ceoBusinessOrganizationPositionRelation= ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq("employee_id", seniorEmpId)
                        .eq("channel", 3)
                        .last("limit 1")
                );
                sfaTaskEntity.setStatus(TaskStatusEnum.READY_PUSH.getStatus());
                processStep = TaskProcessStepEnum.PUSH.getProcessStep();
            }

        }
        // 个人任务
        else if(sfaTaskEntity.getTaskType() == 2){
            // 任务处理人为主办人
            SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskId).eq("assign_type", 1)
                    .eq("status", 1).eq("delete_flag", 0).last("limit 1"));
            if(Objects.isNull(sfaTaskAssignEntity)){
                throw new ApplicationException("主办人信息获取失败");
            }
            ceoBusinessOrganizationPositionRelation= ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", sfaTaskAssignEntity.getAssignUserId())
                    .eq("channel", 3)
                    .last("limit 1")
            );
            // 个人任务直接为处理中
            sfaTaskEntity.setStatus(TaskStatusEnum.PROCESSING.getStatus());
            processStep = TaskProcessStepEnum.PROCESSING.getProcessStep();


            // 保存发布记录
            TaskLogDTO taskLogDTO = new TaskLogDTO();
            taskLogDTO.setTaskId(taskId);
            taskLogDTO.setType(TaskLogTypeEnum.PUSH_TASK.getType());
            taskLogDTO.setProcessUserName(createUserName);
            taskLogDTO.setProcessUserId(createUserId);
            // 获取接受人
            List<SfaTaskAssignEntity> sfaTaskAssignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskId)
                    .eq("status", 1).eq("delete_flag", 0));
            if(!CollectionUtils.isEmpty(sfaTaskAssignEntities)){
                List<String> assigns = sfaTaskAssignEntities.stream().map(SfaTaskAssignEntity::getAssignUserName).collect(Collectors.toList());
                taskLogDTO.setProcessObj(assigns);
            }
            taskLogService.saveLog(taskLogDTO);
        }
        // 部门任务
        else if(sfaTaskEntity.getTaskType() == 3){
            processStep = taskProcessDTO.getProcessStep();
            if(taskProcessDTO.getProcessStep() == TaskProcessStepEnum.SIGN.getProcessStep()){
                sfaTaskEntity.setStatus(TaskStatusEnum.READY_SIGN.getStatus());

            }else{
                sfaTaskEntity.setStatus(TaskStatusEnum.READY_PUSH.getStatus());

            }
            ceoBusinessOrganizationPositionRelation = taskProcessDTO.getAuditPerson();

            if(!taskProcessDTO.getAuditPerson().equals(createUserId)){
                // 抄送给部门审核人
                saveAssign(sfaTaskEntity.getTaskId(),sfaTaskEntity,3,ceoBusinessOrganizationPositionRelation.getEmployeeId(),ceoBusinessOrganizationPositionRelation.getEmployeeName(),taskProcessDTO);

            }

        }
        

        // 保存任务实例保存任务实例
        saveInstance(taskId, processStep, ceoBusinessOrganizationPositionRelation, createUserId, createUserName);

        // 修改任务状态
        sfaTaskMapper.updateById(sfaTaskEntity);

    }


    @Override
    @Transactional
    public void launchMeeting(LaunchMeetingRequest launchMeetingRequest) {
        String meetingTime = StringUtils.isBlank(launchMeetingRequest.getMeetingTime()) ? StringUtils.EMPTY : launchMeetingRequest.getMeetingTime();
        String remark = "开会时间: "+ meetingTime +"<br>" + Optional.ofNullable(launchMeetingRequest.getRemark()).orElse(StringUtils.EMPTY);

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(launchMeetingRequest.getPerson(), RequestUtils.getLoginInfo());
        // 保存开会信息
        SfaTaskMeetingEntity sfaTaskMeetingEntity = new SfaTaskMeetingEntity();
        sfaTaskMeetingEntity.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        sfaTaskMeetingEntity.setTaskId(launchMeetingRequest.getTaskId());

        if(StringUtils.isNotBlank(meetingTime)){
            sfaTaskMeetingEntity.setMeetingSuggestionTime(meetingTime);
        }
        taskMeetingMapper.insert(sfaTaskMeetingEntity);

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(launchMeetingRequest.getTaskId());
        sfaTaskEntity.setTaskTag("待开会议");
        sfaTaskMapper.updateById(sfaTaskEntity);

        List<NotifyPO> notifyPOS = new ArrayList<>();

        // 推送消息给主办、协办、项目管理部
        List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, launchMeetingRequest.getTaskId()));

        // 保存记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(launchMeetingRequest.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.LAUNCH_MEETING.getType());
        taskLogDTO.setRemark(remark);
        taskLogDTO.setProcessUserName(personInfo.getEmployeeName());
        taskLogDTO.setProcessUserId(personInfo.getEmployeeId());
        taskLogService.saveLog(taskLogDTO);


        assignEntities.forEach(e -> {
            NotifyPO po = new NotifyPO();
            po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【待会议确认】");
            po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
            po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【待会议确认】");
            po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
            po.setEmployeeId(e.getAssignUserId());
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        });
        notifyService.saveBatch(notifyPOS);

    }

    private void doProcess(Long taskId,String person,String remark,int taskStatus,int processStep,int logType,String taskTag) {

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);

        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person, RequestUtils.getLoginInfo());

        sfaTaskEntity.setStatus(taskStatus);
        sfaTaskEntity.setTaskTag(taskTag);
        sfaTaskEntity.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        sfaTaskMapper.updateById(sfaTaskEntity);

        // 流程修改
        // 获取当前任务流程实力
        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new LambdaQueryWrapper<SfaTaskInstanceEntity>().eq(SfaTaskInstanceEntity::getTaskId, taskId).eq(SfaTaskInstanceEntity::getDeleteFlag, 0));
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("流程实力获取失败");
        }

        Long recordId = sfaTaskInstanceEntity.getRecordId();
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(recordId);
        sfaTaskInstanceRecordEntity.setProcessUserId(personInfo.getEmployeeId());
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setProcessUserName(personInfo.getEmployeeName());
        // 当前流程设置未完结
        sfaTaskInstanceRecordEntity.setProcessResult(1);
        sfaTaskInstanceRecordEntity.setRemark(remark);
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("流程明细获取失败");
        }

        // 新建下级流程明细
        SfaTaskInstanceRecordEntity nextRecord = new SfaTaskInstanceRecordEntity();
        nextRecord.setProcessStep(processStep);
        nextRecord.setStatus(1);
        nextRecord.setDeleteFlag(0);
        nextRecord.setProcessResult(0);
        nextRecord.setCreateTime(LocalDateTime.now());
        nextRecord.setPrevRecord(recordId);
        // 处理人为老板
        String seniorHrEmployeeId = configMapper.getValueByCode("zw_senior_hr_employee_id");
        CeoBusinessOrganizationPositionRelation nextProcessUser = checkCustomerService.getPersonInfo(seniorHrEmployeeId, RequestUtils.getLoginInfo());
        nextRecord.setProcessUserId(nextProcessUser.getEmployeeId());
        nextRecord.setProcessUserName(nextProcessUser.getEmployeeName());

        sfaTaskInstanceRecordMapper.insert(nextRecord);

        sfaTaskInstanceRecordEntity.setNextRecord(nextRecord.getRecordId());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);


        sfaTaskInstanceEntity.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        sfaTaskInstanceEntity.setRecordId(nextRecord.getRecordId());
        sfaTaskInstanceEntity.setProcessStep(nextRecord.getProcessStep());
        sfaTaskInstanceEntity.setProcessResult(nextRecord.getProcessResult());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);


        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskId);
        taskLogDTO.setType(logType);
        taskLogDTO.setRemark(remark);
        taskLogDTO.setProcessUserName(personInfo.getEmployeeName());
        taskLogDTO.setProcessUserId(personInfo.getEmployeeId());
        taskLogService.saveLog(taskLogDTO);
    }

    @Override
    @Transactional
    public void meetingAudit(TaskAuditRequest taskAuditRequest) {
        doProcess(taskAuditRequest.getTaskId()
                ,taskAuditRequest.getPerson()
                ,taskAuditRequest.getRemark()
                ,TaskStatusEnum.ENDING_FINISH.getStatus()
                ,TaskProcessStepEnum.ENDING_FINISH.getProcessStep()
                ,TaskLogTypeEnum.AUDIT_MEETING.getType(),
                taskAuditRequest.getTaskTag()
        );
    }

    @Override
    public void meetingAuditFinish(TaskAuditRequest taskAuditRequest) {
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskAuditRequest.getTaskId());

        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        // 如果当前任务处于审核完结状态，自动换成审核完结状态，并创建一条确认完结记录
        if(Objects.equals(sfaTaskEntity.getStatus(), TaskStatusEnum.MEETING_CONFIRM.getStatus())){
            doProcess(taskAuditRequest.getTaskId()
                    ,taskAuditRequest.getPerson()
                    ,taskAuditRequest.getRemark()
                    ,TaskStatusEnum.ENDING_FINISH.getStatus()
                    ,TaskProcessStepEnum.ENDING_FINISH.getProcessStep()
                    ,TaskLogTypeEnum.AUDIT_MEETING.getType(),
                    taskAuditRequest.getTaskTag()
            );
        }

        // 设置当当前任务完结
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(taskAuditRequest.getPerson(), RequestUtils.getLoginInfo());
        sfaTaskEntity.setStatus(TaskStatusEnum.FINISH.getStatus());
        sfaTaskEntity.setTaskTag(taskAuditRequest.getTaskTag());
        sfaTaskEntity.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        sfaTaskMapper.updateById(sfaTaskEntity);

        // 流程修改
        // 获取当前任务流程实力
        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new LambdaQueryWrapper<SfaTaskInstanceEntity>().eq(SfaTaskInstanceEntity::getTaskId, sfaTaskEntity.getTaskId()).eq(SfaTaskInstanceEntity::getDeleteFlag, 0));
        sfaTaskInstanceEntity.setProcessResult(1);
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);


        Long recordId = sfaTaskInstanceEntity.getRecordId();
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(recordId);
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("流程明细获取失败");
        }
        sfaTaskInstanceRecordEntity.setRemark(taskAuditRequest.getRemark());
        sfaTaskInstanceRecordEntity.setProcessResult(1);
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setProcessUserId(personInfo.getEmployeeId());
        sfaTaskInstanceRecordEntity.setProcessUserName(personInfo.getEmployeeName());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

    }

    private void saveAssign(Long taskId, SfaTaskEntity sfaTaskEntity, int type, String employeeId, String employeeName, TaskProcessDTO taskProcessDTO) {
        // 检查是否有给部门负责人
        SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, taskId).eq(SfaTaskAssignEntity::getDeleteFlag, 0).eq(SfaTaskAssignEntity::getAssignUserId, employeeId).last("limit 1"));

        if(Objects.isNull(sfaTaskAssignEntity)){
            sfaTaskAssignEntity = new SfaTaskAssignEntity();
            sfaTaskAssignEntity.init(sfaTaskEntity.getCreateUserId(),sfaTaskEntity.getCreateUserName());
            sfaTaskAssignEntity.setAssignType(type);
            sfaTaskAssignEntity.setStatus(1);
            sfaTaskAssignEntity.setTaskId(taskId);
            sfaTaskAssignEntity.setAssignDeptCode(taskProcessDTO.getDeptCode());
            sfaTaskAssignEntity.setAssignDeptName(taskProcessDTO.getDeptName());
            sfaTaskAssignEntity.setAssignUserId(employeeId);
            sfaTaskAssignEntity.setAssignUserName(employeeName);
            sfaTaskAssignMapper.insert(sfaTaskAssignEntity);
        }
    }


    private TaskProcessDTO getProcessStep(SfaTaskEntity sfaTaskEntity, String createUserId) {
        TaskProcessDTO taskProcessDTO = new TaskProcessDTO();
        Integer taskType = sfaTaskEntity.getTaskType();
        if(1 == taskType){
            taskProcessDTO.setProcessStep(TaskProcessStepEnum.PUSH.getProcessStep());
        }
        else if(2 == taskType){
            taskProcessDTO.setProcessStep(TaskProcessStepEnum.SIGN.getProcessStep());
        }

        else if(3 == taskType){
            // 获取部门信息
            SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", sfaTaskEntity.getTaskId()).eq("assign_type", 1)
                    .eq("status", 1).eq("delete_flag", 0).last("limit 1"));

            String deptCode = sfaTaskEntity.getDeptCode();
            DepartEntity entity = deptMapper.selectOne(new LambdaQueryWrapper<DepartEntity>().eq(DepartEntity::getDeptCode, deptCode).eq(DepartEntity::getDeleteFlag, 0));
            String leaderId = entity.getLeaderId();
            if(StringUtils.isBlank(leaderId)){
                leaderId = getDeptLeadId(entity);
            }
            // 部门领导发起的任务不需要审核
            if(leaderId.equals(createUserId)){
                taskProcessDTO.setProcessStep(TaskProcessStepEnum.SIGN.getProcessStep());

                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, sfaTaskAssignEntity.getAssignUserId())
                        .last("limit 1")
                );
                taskProcessDTO.setAuditPerson(ceoBusinessOrganizationPositionRelation);

                return taskProcessDTO;
            }

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, leaderId)
                    .last("limit 1")
            );
            taskProcessDTO.setAuditPerson(ceoBusinessOrganizationPositionRelation);
            taskProcessDTO.setDeptCode(deptCode);
            taskProcessDTO.setDeptName(entity.getDeptName());
            taskProcessDTO.setProcessStep(TaskProcessStepEnum.PUSH.getProcessStep());

        }


        return taskProcessDTO;
    }





    private String getDeptLeadId(DepartEntity deptEntity) {
        // 获取上级部门
        Integer superiorDeptId = deptEntity.getSuperiorDeptId();
        if(Objects.isNull(superiorDeptId)){
            return configMapper.getValueByCode("zw_senior_hr_employee_id");
        }

        DepartEntity entity = deptMapper.selectById(superiorDeptId);
        if(StringUtils.isNotBlank(entity.getLeaderId())){
            return entity.getLeaderId();
        }

        return getDeptLeadId(entity);
    }

    @Override
    @Transactional
    public void changeProcessUser(Long taskId, TaskAssignDTO mainProcessUser) {
        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskId).eq("delete_flag",0));
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity);
        sfaTaskInstanceRecordEntity.setProcessUserName(mainProcessUser.getEmpName());
        sfaTaskInstanceRecordEntity.setProcessUserId(mainProcessUser.getEmpId());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);
    }

    @Override
    @Transactional
    public void suspend(Long taskId,Integer suspend,String remark, String processUserId, String processUserName) {
        log.info("【task pending】taskId:{}",taskId);
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);

        Integer status = sfaTaskEntity.getStatus();
        if(status == TaskStatusEnum.CLOSED.getStatus()){
            throw new ApplicationException("当前任务已关闭，无法挂起");
        }

        sfaTaskEntity.setSuspend(suspend);
        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskEntity.setUpdateUserId(processUserId);
        sfaTaskEntity.setUpdateUserName(processUserName);
        sfaTaskMapper.updateById(sfaTaskEntity);

        // 保存发布记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskId);
        if(suspend == 1){
            taskLogDTO.setType(TaskLogTypeEnum.SUSPEND.getType());
        }else{
            taskLogDTO.setType(TaskLogTypeEnum.UN_SUSPEND.getType());
        }
        taskLogDTO.setProcessUserName(processUserName);
        taskLogDTO.setProcessUserId(processUserId);
        taskLogDTO.setRemark(remark);
        taskLogService.saveLog(taskLogDTO);
    }

    @Override
    @Transactional
    public void closed(Long taskId,String remark, String processUserId, String processUserName) {
        log.info("【task pending】taskId:{}",taskId);

        log.info("【task pending】taskId:{}",taskId);
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);

        sfaTaskEntity.setStatus(TaskStatusEnum.CLOSED.getStatus());
        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskEntity.setUpdateUserId(processUserId);
        sfaTaskEntity.setUpdateUserName(processUserName);
        sfaTaskMapper.updateById(sfaTaskEntity);


        // 保存发布记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskId);
        taskLogDTO.setType(TaskLogTypeEnum.CLOSED.getType());
        taskLogDTO.setProcessUserName(processUserName);
        taskLogDTO.setProcessUserId(processUserId);
        taskLogDTO.setRemark(remark);
        taskLogService.saveLog(taskLogDTO);

        //任务关闭消息通知项目管理/主办人
        sendMsg(processUserId,sfaTaskEntity);

    }

    private void sendMsg(String processUserId,SfaTaskEntity sfaTaskEntity){
        //任务关闭消息通知项目管理/主办人
        List<String> employeeId = new ArrayList<>();
        //leo关闭通知项目管理/主办人
        if(processUserId.equals(configMapper.getValueByCode("zw_senior_hr_employee_id"))) {
            String projectManagerDepartment = configMapper.getValueByCode("project_manager_department");
            employeeId = organizationMapper.selectEmployeeIdByDeptId(Long.parseLong(projectManagerDepartment));
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId())
                    .eq("status", 1).eq("delete_flag", 0)
                    .eq("assign_type", 1));
            for (SfaTaskAssignEntity assignEntity : assignEntities) {
                if (!employeeId.contains(assignEntity.getAssignUserId())) {
                    employeeId.add(assignEntity.getAssignUserId());
                }
            }
        }else{
            //项目管理关闭通知主办人
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId())
                    .eq("status", 1).eq("delete_flag", 0)
                    .eq("assign_type", 1));
            for (SfaTaskAssignEntity assignEntity : assignEntities) {
                if (!employeeId.contains(assignEntity.getAssignUserId())) {
                    employeeId.add(assignEntity.getAssignUserId());
                }
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(employeeId)) {
            List<NotifyPO> notifyPOS = new ArrayList<>();
            employeeId.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【关闭】");
                po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【关闭】");
                po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                po.setEmployeeId(e);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
    }

    @Override
    @Transactional
    public void modifyDeadline(ModifyDeadlineRequest modifyDeadlineRequest) {
        Long taskId = modifyDeadlineRequest.getTaskId();
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(modifyDeadlineRequest.getPerson(), RequestUtils.getLoginInfo());

        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }


        sfaTaskEntity.setDeadline(LocalDate.parse(modifyDeadlineRequest.getDeadline()).atStartOfDay());
        sfaTaskEntity.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());

        // 检查是否修改任务性质
        LocalDateTime createTime = sfaTaskEntity.getCreateTime();
        Duration duration = Duration.between(createTime,LocalDate.parse(modifyDeadlineRequest.getDeadline()).atStartOfDay());
        long l = duration.toDays();
        if(l > 30){
            sfaTaskEntity.setTaskNature(1);
        }else{
            sfaTaskEntity.setTaskNature(2);
        }

        sfaTaskMapper.updateById(sfaTaskEntity);


        // 保存发布记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskId);

        taskLogDTO.setType(TaskLogTypeEnum.MODIFY_DEADLINE.getType());
        taskLogDTO.setProcessUserName(personInfo.getEmployeeName());
        taskLogDTO.setProcessUserId(personInfo.getEmployeeId());
        taskLogDTO.setRemark(modifyDeadlineRequest.getRemark());
        taskLogService.saveLog(taskLogDTO);
    }



    private void saveInstance(Long taskId,int processStep, CeoBusinessOrganizationPositionRelation position, String createUserId, String createUserName) {
        SfaTaskInstanceEntity sfaTaskInstanceEntity = new SfaTaskInstanceEntity();
        sfaTaskInstanceEntity.init(createUserId,createUserName);
        sfaTaskInstanceEntity.setStatus(1);
        sfaTaskInstanceEntity.setRecordId(0L);
        sfaTaskInstanceEntity.setTaskId(taskId);
        sfaTaskInstanceEntity.setProcessStep(processStep);
        if(processStep == TaskProcessStepEnum.PROCESSING.getProcessStep()){
            sfaTaskInstanceEntity.setProcessResult(3);
        }else{
            sfaTaskInstanceEntity.setProcessResult(0);
        }

        sfaTaskInstanceMapper.insert(sfaTaskInstanceEntity);

        // 保存记录
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = new SfaTaskInstanceRecordEntity();
        sfaTaskInstanceRecordEntity.setProcessStep(processStep);
        if(processStep == TaskProcessStepEnum.PROCESSING.getProcessStep()){
            sfaTaskInstanceRecordEntity.setProcessResult(3);
        }else{
            sfaTaskInstanceRecordEntity.setProcessResult(0);
        }

        sfaTaskInstanceRecordEntity.setProcessUserId(position.getEmployeeId());
        sfaTaskInstanceRecordEntity.setProcessUserName(position.getEmployeeName());
        sfaTaskInstanceRecordEntity.setCreateTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setStatus(1);
        sfaTaskInstanceRecordEntity.setDeleteFlag(0);
        sfaTaskInstanceRecordMapper.insert(sfaTaskInstanceRecordEntity);

        // 流程绑定
        sfaTaskInstanceEntity.setRecordId(sfaTaskInstanceRecordEntity.getRecordId());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);
    }
}
