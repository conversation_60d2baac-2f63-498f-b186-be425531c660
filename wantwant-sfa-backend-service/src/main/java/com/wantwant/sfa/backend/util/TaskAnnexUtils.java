package com.wantwant.sfa.backend.util;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 任务附件转换工具类
 * 用于TaskAnnexRequest对象列表与字符串之间的相互转换
 * 
 * @Description: 任务附件序列化/反序列化工具类
 * @Author: assistant
 * @Date: 2024/12/18
 */
public class TaskAnnexUtils {

    /**
     * 将TaskAnnexRequest对象列表转换为字符串
     * 
     * @param annexList 附件对象列表
     * @param separator 分隔符，用于分割name和url，默认为"$$"
     * @return 转换后的字符串，格式为：name1$$url1,name2$$url2
     */
    public static String annexListToString(List<TaskAnnexRequest> annexList, String separator) {
        if (CollectionUtils.isEmpty(annexList)) {
            return StringUtils.EMPTY;
        }
        
        if (StringUtils.isBlank(separator)) {
            separator = "$$";
        }
        
        final String finalSeparator = separator;
        List<String> annexStrings = annexList.stream()
                .filter(Objects::nonNull)
                .filter(annex -> StringUtils.isNotBlank(annex.getName()) && StringUtils.isNotBlank(annex.getUrl()))
                .map(annex -> annex.getName() + finalSeparator + annex.getUrl())
                .collect(Collectors.toList());
        
        return String.join(",", annexStrings);
    }

    /**
     * 将TaskAnnexRequest对象列表转换为字符串（使用默认分隔符$$）
     * 
     * @param annexList 附件对象列表
     * @return 转换后的字符串
     */
    public static String annexListToString(List<TaskAnnexRequest> annexList) {
        return annexListToString(annexList, "$$");
    }

    /**
     * 将字符串转换为TaskAnnexRequest对象列表
     * 
     * @param annexString 附件字符串，格式为：name1$$url1,name2$$url2
     * @param separator 分隔符，用于分割name和url，默认为"$$"
     * @return 转换后的TaskAnnexRequest对象列表
     */
    public static List<TaskAnnexRequest> stringToAnnexList(String annexString, String separator) {
        if (StringUtils.isBlank(annexString)) {
            return new ArrayList<>();
        }
        
        if (StringUtils.isBlank(separator)) {
            separator = "$$";
        }
        
        List<String> annexStringList = Arrays.asList(annexString.split(","));
        List<TaskAnnexRequest> annexRequests = new ArrayList<>();
        
        final String finalSeparator = separator;
        annexStringList.forEach(annexStr -> {
            if (StringUtils.isNotBlank(annexStr)) {
                String[] parts = annexStr.split(finalSeparator.equals("$$") ? "\\$\\$" : finalSeparator);
                if (parts.length == 2) {
                    TaskAnnexRequest taskAnnexRequest = new TaskAnnexRequest();
                    taskAnnexRequest.setName(parts[0]);
                    taskAnnexRequest.setUrl(parts[1]);
                    annexRequests.add(taskAnnexRequest);
                }
            }
        });
        
        return annexRequests;
    }

    /**
     * 将字符串转换为TaskAnnexRequest对象列表（使用默认分隔符$$）
     * 
     * @param annexString 附件字符串
     * @return 转换后的TaskAnnexRequest对象列表
     */
    public static List<TaskAnnexRequest> stringToAnnexList(String annexString) {
        return stringToAnnexList(annexString, "$$");
    }
}
