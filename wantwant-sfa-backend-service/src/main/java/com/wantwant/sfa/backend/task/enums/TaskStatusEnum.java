package com.wantwant.sfa.backend.task.enums;

import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午11:23
 */
@AllArgsConstructor
public enum         TaskStatusEnum {
    DRAFT(10,"草稿"),
    READY_PUSH(20,"待发布"),
    AUDIT_PUSH(21,"待确认发布"),
    READY_SIGN(30,"待签收"),
    PROCESSING(40,"进行中"),
    READY_AUDIT(50,"待审核"),
    FINAL_AUDIT(51,"待确认完结"),
    MEETING_CONFIRM(52,"待会议确认"),
    ENDING_FINISH(53,"待会议审核确认"),
    FINISH(60,"已办结"),
    RE_SIGN(70,"重办待签收"),
    CLOSED(80,"已关闭");


    private Integer status;

    private String name;

    public static String getStatusName(Integer status, LocalDateTime deadline) {

        TaskStatusEnum[] values = TaskStatusEnum.values();
        for(TaskStatusEnum e: values){


            if(Objects.equals(e.getStatus(), status)){
                if(Objects.equals(e.getStatus(), DRAFT.getStatus()) || Objects.equals(e.getStatus(), CLOSED.getStatus()) || Objects.equals(e.getStatus(), FINISH.getStatus())){
                    return e.getName();
                }
                else if(deadline.isBefore(LocalDate.now().atStartOfDay())){
                    return e.getName() + " (已逾期)";
                }else if(deadline.isEqual(LocalDate.now().atStartOfDay())){
                    return e.getName() + " (已到期)";
                }else{
                    return e.getName();
                }

            }
        }
        return null;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
