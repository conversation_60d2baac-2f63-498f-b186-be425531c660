package com.wantwant.sfa.backend.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.task.*;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.task.dto.TaskLogDTO;
import com.wantwant.sfa.backend.task.dto.TaskRedoneDTO;
import com.wantwant.sfa.backend.task.dto.TaskRefuseDTO;
import com.wantwant.sfa.backend.task.dto.TaskProcessCommand;
import com.wantwant.sfa.backend.task.entity.SfaTaskAssignEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskInstanceEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskInstanceRecordEntity;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskProcessStepEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.service.ITaskAuditService;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/10/下午5:21
 */
@Service
@Slf4j
public class TaskAuditService implements ITaskAuditService {
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private SfaTaskInstanceMapper sfaTaskInstanceMapper;
    @Autowired
    private SfaTaskInstanceRecordMapper sfaTaskInstanceRecordMapper;
    @Autowired
    private SfaTaskAssignMapper sfaTaskAssignMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ITaskLogService taskLogService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    @Transactional
    public void finish(Long taskId, String processUserId, String processUserName,String taskTag,String remark) {
        log.info("【task finish】taskId:{}",taskId);

        doProcess(TaskProcessCommand.builder()
                .taskId(taskId)
                .deadline(null)
                .processUserId(processUserId)
                .processUserName(processUserName)
                .remark(remark)
                .result(1)
                .taskTag(taskTag)
                .build());
    }

    private void doProcess(TaskProcessCommand command) {
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(command.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        Integer status = sfaTaskEntity.getStatus();
        if(!Objects.equals(status, TaskStatusEnum.READY_AUDIT.getStatus()) && !Objects.equals(status, TaskStatusEnum.FINAL_AUDIT.getStatus())){
            throw new ApplicationException("任务已关闭或已完结");
        }

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>()
                .eq("task_id", command.getTaskId())
                .eq("delete_flag", 0)
        );
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("任务流程实例获取失败");
        }

        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("任务记录获取失败");
        }
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setProcessUserId(command.getProcessUserId());
        taskLogDTO.setRemark(command.getRemark());
        taskLogDTO.setProcessUserName(command.getProcessUserName());
        taskLogDTO.setTaskId(command.getTaskId());
        taskLogDTO.setAppendix(command.getAppendix());
        sfaTaskEntity.setTaskTag(command.getTaskTag());
        // 任务完成
        if(command.getResult() == 1){
            sfaTaskInstanceRecordEntity.setProcessResult(1);
            sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
            sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

            // 设置流程结束
            sfaTaskInstanceEntity.setProcessResult(1);
            sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
            sfaTaskInstanceEntity.setUpdateUserId(command.getProcessUserId());
            sfaTaskInstanceEntity.setUpdateUserName(command.getProcessUserName());
            sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);

            // 任务表状态设置为完结
            sfaTaskEntity.setStatus(TaskStatusEnum.FINISH.getStatus());
            sfaTaskEntity.setTaskTag(command.getTaskTag());
            sfaTaskEntity.setUpdateTime(LocalDateTime.now());
            sfaTaskEntity.setUpdateUserId(command.getProcessUserId());
            sfaTaskEntity.setUpdateUserName(command.getProcessUserName());
            sfaTaskMapper.updateById(sfaTaskEntity);

            taskLogDTO.setType(TaskLogTypeEnum.FINISH.getType());
        }else{
            // 新建1条待签收的任务
            SfaTaskInstanceRecordEntity nextTaskRecord = new SfaTaskInstanceRecordEntity();
            nextTaskRecord.setProcessStep(TaskProcessStepEnum.SIGN.getProcessStep());
            nextTaskRecord.setProcessResult(0);

            // 获取主办人
            SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", command.getTaskId())
                    .eq("assign_type", 1).eq("status", 1).eq("delete_flag", 0)
                    .last("limit 1")
            );
            if(Objects.isNull(sfaTaskAssignEntity)){
                throw new ApplicationException("无主办指派人");
            }

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", sfaTaskAssignEntity.getAssignUserId())
                    .eq("channel", 3).last("limit 1")
            );

            nextTaskRecord.setProcessUserId(ceoBusinessOrganizationPositionRelation.getEmployeeId());
            nextTaskRecord.setProcessUserName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
            nextTaskRecord.setCreateTime(LocalDateTime.now());
            nextTaskRecord.setStatus(1);
            nextTaskRecord.setDeleteFlag(0);
            nextTaskRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
            sfaTaskInstanceRecordMapper.insert(nextTaskRecord);

            // 重办
            sfaTaskInstanceRecordEntity.setProcessResult(2);
            sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
            sfaTaskInstanceRecordEntity.setRemark(command.getRemark());
            sfaTaskInstanceRecordEntity.setNextRecord(nextTaskRecord.getRecordId());
            sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

            // 绑定流程表
            sfaTaskInstanceEntity.setProcessResult(nextTaskRecord.getProcessResult());
            sfaTaskInstanceEntity.setProcessStep(nextTaskRecord.getProcessStep());
            sfaTaskInstanceEntity.setRecordId(nextTaskRecord.getRecordId());
            sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
            sfaTaskInstanceEntity.setUpdateUserId(command.getProcessUserId());
            sfaTaskInstanceEntity.setUpdateUserName(command.getProcessUserName());
            sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);

            // 修改任务状态
            sfaTaskEntity.setStatus(TaskStatusEnum.RE_SIGN.getStatus());
            sfaTaskEntity.setDeadline(command.getDeadline().atStartOfDay());
            sfaTaskEntity.update(command.getProcessUserId(),command.getProcessUserName());
            sfaTaskMapper.updateById(sfaTaskEntity);

            taskLogDTO.setRemark(command.getRemark());
            taskLogDTO.setType(TaskLogTypeEnum.REDONE.getType());

        }

        // 操作记录
        taskLogService.saveLog(taskLogDTO);
    }

    @Override
    @Transactional
    public void redone(TaskRedoneDTO taskRedoneDTO) {
        log.info("【task redone】taskId:{}",taskRedoneDTO.getTaskId());

        doProcess(TaskProcessCommand.builder()
                .taskId(taskRedoneDTO.getTaskId())
                .deadline(taskRedoneDTO.getDeadline())
                .processUserId(taskRedoneDTO.getProcessUserId())
                .processUserName(taskRedoneDTO.getProcessUserName())
                .remark(taskRedoneDTO.getRemark())
                .result(2)
                .taskTag(taskRedoneDTO.getTaskTag())
                .appendix(taskRedoneDTO.getAppendix())
                .build());

        if(Objects.nonNull(taskRedoneDTO.getSendMessage()) && taskRedoneDTO.getSendMessage() == 1){
            SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskRedoneDTO.getTaskId());
            sfaTaskEntity.setTaskTag(taskRedoneDTO.getTaskTag());
            sfaTaskMapper.updateById(sfaTaskEntity);
            // 获取所有的参与者
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskRedoneDTO.getTaskId()).eq("status", 1).eq("delete_flag", 0));
            if(!CollectionUtils.isEmpty(assignEntities)){
                List<NotifyPO> notifyPOS = new ArrayList<>();
                assignEntities.forEach(e -> {
                    NotifyPO po = new NotifyPO();
                    po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【重办】");
                    po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                    po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【重办】");
                    po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                    po.setEmployeeId(e.getAssignUserId());
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);

                });

                notifyService.saveBatch(notifyPOS);
            }
        }
    }

    /**
     * 任务驳回
     *
     * @param taskRefuseDTO
     */
    @Override
    @Transactional
    public void refuse(TaskRefuseDTO taskRefuseDTO) {
        log.info("【task refuse】taskId:{}", taskRefuseDTO.getTaskId());
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskRefuseDTO.getTaskId());
        if (Objects.isNull(sfaTaskEntity)) {
            throw new ApplicationException("任务不存在");
        }

        Integer status = sfaTaskEntity.getStatus();
        if (!Objects.equals(status, TaskStatusEnum.READY_AUDIT.getStatus()) && !Objects.equals(status, TaskStatusEnum.FINAL_AUDIT.getStatus())) {
            throw new ApplicationException("任务已关闭或已完结");
        }

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>()
                .eq("task_id", taskRefuseDTO.getTaskId())
                .eq("delete_flag", 0)
        );
        if (Objects.isNull(sfaTaskInstanceEntity)) {
            throw new ApplicationException("任务流程实例获取失败");
        }

        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        if (Objects.isNull(sfaTaskInstanceRecordEntity)) {
            throw new ApplicationException("任务记录获取失败");
        }
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setProcessUserId(taskRefuseDTO.getProcessUserId());
        taskLogDTO.setProcessUserName(taskRefuseDTO.getProcessUserName());
        taskLogDTO.setTaskId(taskRefuseDTO.getTaskId());
        taskLogDTO.setAppendix(taskRefuseDTO.getAppendix());

        //修改sfa_task表状态为进行中
        //修改sfa_task_instance表状态为进行中
        //修改sfa_task_instance_record表送审/老板送审数据为驳回状态
        //添加sfa_task_instance_record表进行中未处理数据
        //添加sfa_task_log表数据


        // 新建1条进行中的任务
        SfaTaskInstanceRecordEntity nextTaskRecord = new SfaTaskInstanceRecordEntity();
        nextTaskRecord.setProcessStep(TaskProcessStepEnum.PROCESSING.getProcessStep());
        nextTaskRecord.setProcessResult(3);

        // 获取主办人
        SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskRefuseDTO.getTaskId())
                .eq("assign_type", 1).eq("status", 1).eq("delete_flag", 0)
                .last("limit 1")
        );
        if (Objects.isNull(sfaTaskAssignEntity)) {
            throw new ApplicationException("无主办指派人");
        }

        nextTaskRecord.setProcessUserId(sfaTaskAssignEntity.getAssignUserId());
        nextTaskRecord.setProcessUserName(sfaTaskAssignEntity.getAssignUserName());
        nextTaskRecord.setCreateTime(LocalDateTime.now());
        nextTaskRecord.setStatus(1);
        nextTaskRecord.setDeleteFlag(0);
        nextTaskRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
        sfaTaskInstanceRecordMapper.insert(nextTaskRecord);

        // 驳回
        sfaTaskInstanceRecordEntity.setProcessResult(2);
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setRemark(taskRefuseDTO.getRemark());
        sfaTaskInstanceRecordEntity.setNextRecord(nextTaskRecord.getRecordId());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

        // 绑定流程表
        sfaTaskInstanceEntity.setProcessResult(nextTaskRecord.getProcessResult());
        sfaTaskInstanceEntity.setProcessStep(nextTaskRecord.getProcessStep());
        sfaTaskInstanceEntity.setRecordId(nextTaskRecord.getRecordId());
        sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskInstanceEntity.setUpdateUserId(taskRefuseDTO.getProcessUserId());
        sfaTaskInstanceEntity.setUpdateUserName(taskRefuseDTO.getProcessUserName());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);

        // 修改任务状态
        sfaTaskEntity.setStatus(TaskStatusEnum.PROCESSING.getStatus());
        sfaTaskEntity.setTaskTag(taskRefuseDTO.getTaskTag());
        sfaTaskEntity.update(taskRefuseDTO.getProcessUserId(), taskRefuseDTO.getProcessUserName());
        sfaTaskMapper.updateById(sfaTaskEntity);

        taskLogDTO.setRemark(taskRefuseDTO.getRemark());
        taskLogDTO.setType(TaskLogTypeEnum.REFUSE.getType());

        // 操作记录
        taskLogService.saveLog(taskLogDTO);

        //任务驳回消息通知项目管理/主办人
        sendMsg(taskRefuseDTO.getProcessUserId(),sfaTaskEntity);

    }

    private void sendMsg(String processUserId,SfaTaskEntity sfaTaskEntity){
        //任务驳回消息通知项目管理/主办人
        List<String> employeeId = new ArrayList<>();
        //leo驳回通知项目管理/主办人
        if(processUserId.equals(configMapper.getValueByCode("zw_senior_hr_employee_id"))) {
            String projectManagerDepartment = configMapper.getValueByCode("project_manager_department");
            employeeId = organizationMapper.selectEmployeeIdByDeptId(Long.parseLong(projectManagerDepartment));
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId())
                    .eq("status", 1).eq("delete_flag", 0)
                    .eq("assign_type", 1));
            for (SfaTaskAssignEntity assignEntity : assignEntities) {
                if (!employeeId.contains(assignEntity.getAssignUserId())) {
                    employeeId.add(assignEntity.getAssignUserId());
                }
            }
        }else{
            //项目管理驳回通知主办人
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId())
                    .eq("status", 1).eq("delete_flag", 0)
                    .eq("assign_type", 1));
            for (SfaTaskAssignEntity assignEntity : assignEntities) {
                if (!employeeId.contains(assignEntity.getAssignUserId())) {
                    employeeId.add(assignEntity.getAssignUserId());
                }
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(employeeId)) {
            List<NotifyPO> notifyPOS = new ArrayList<>();
            employeeId.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【任务驳回】");
                po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【任务驳回】");
                po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                po.setEmployeeId(e);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
    }
}
