#!/usr/bin/env python3
"""
增强版Cursor IDE使用情况分析器
提供更详细的统计和更准确的采纳率估算
"""

import os
import json
import sqlite3
import re
from datetime import datetime, timedelta
from pathlib import Path
import platform
from typing import Dict, List, Tuple
import argparse

class EnhancedCursorAnalyzer:
    def __init__(self, days_back: int = 7):
        self.system = platform.system()
        self.cursor_dir = self._find_cursor_directory()
        self.days_back = days_back
        self.start_date = datetime.now() - timedelta(days=days_back)
        self.end_date = datetime.now()
        
    def _find_cursor_directory(self) -> Path:
        """查找Cursor的数据目录"""
        if self.system == "Darwin":  # macOS
            base_dir = Path.home() / "Library" / "Application Support" / "Cursor"
        elif self.system == "Windows":
            base_dir = Path(os.environ.get("APPDATA", "")) / "Cursor"
        else:  # Linux
            base_dir = Path.home() / ".config" / "Cursor"
        
        possible_paths = [base_dir / "User", base_dir]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        return Path(".")
    
    def analyze_with_git_context(self) -> Dict:
        """结合Git提交记录分析采纳率"""
        print("🔍 开始增强分析...")
        
        # 获取基础统计
        basic_stats = self._get_basic_stats()
        
        # 分析Git提交记录
        git_stats = self._analyze_git_commits()
        
        # 计算更准确的采纳率
        adoption_stats = self._calculate_adoption_rate(basic_stats, git_stats)
        
        return {
            **basic_stats,
            'git_analysis': git_stats,
            'adoption_analysis': adoption_stats,
            'daily_breakdown': self._get_daily_breakdown()
        }
    
    def _get_basic_stats(self) -> Dict:
        """获取基础统计信息"""
        workspace_storage = self.cursor_dir / "workspaceStorage"
        total_conversations = 0
        total_generated_lines = 0
        total_code_blocks = 0
        
        if workspace_storage.exists():
            for workspace in workspace_storage.iterdir():
                if workspace.is_dir():
                    state_file = workspace / "state.vscdb"
                    if state_file.exists():
                        stats = self._analyze_sqlite_file(state_file)
                        total_conversations += stats.get('conversations', 0)
                        total_generated_lines += stats.get('generated_lines', 0)
                        total_code_blocks += stats.get('code_blocks', 0)
        
        return {
            'total_conversations': total_conversations,
            'total_generated_lines': total_generated_lines,
            'total_code_blocks': total_code_blocks
        }
    
    def _analyze_git_commits(self) -> Dict:
        """分析Git提交记录"""
        git_stats = {
            'total_commits': 0,
            'lines_added': 0,
            'lines_deleted': 0,
            'files_changed': 0,
            'commit_messages': []
        }
        
        try:
            # 检查是否在Git仓库中
            if not (Path.cwd() / ".git").exists():
                return git_stats
            
            # 获取指定时间范围内的提交
            since_date = self.start_date.strftime('%Y-%m-%d')
            
            import subprocess
            
            # 获取提交统计
            cmd = f"git log --since='{since_date}' --pretty=format:'%H|%s|%ad' --date=short --numstat"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                current_commit = None
                
                for line in lines:
                    if '|' in line and len(line.split('|')) == 3:
                        # 提交信息行
                        hash_val, message, date = line.split('|')
                        current_commit = {
                            'hash': hash_val,
                            'message': message,
                            'date': date,
                            'files': [],
                            'lines_added': 0,
                            'lines_deleted': 0
                        }
                        git_stats['commit_messages'].append(message)
                        git_stats['total_commits'] += 1
                    elif line.strip() and current_commit:
                        # 文件变更统计行
                        parts = line.split('\t')
                        if len(parts) >= 3:
                            added = int(parts[0]) if parts[0].isdigit() else 0
                            deleted = int(parts[1]) if parts[1].isdigit() else 0
                            filename = parts[2]
                            
                            current_commit['lines_added'] += added
                            current_commit['lines_deleted'] += deleted
                            current_commit['files'].append(filename)
                            
                            git_stats['lines_added'] += added
                            git_stats['lines_deleted'] += deleted
                            git_stats['files_changed'] += 1
                            
        except Exception as e:
            print(f"Git分析出错: {e}")
        
        return git_stats
    
    def _calculate_adoption_rate(self, basic_stats: Dict, git_stats: Dict) -> Dict:
        """计算更准确的采纳率"""
        generated_lines = basic_stats['total_generated_lines']
        committed_lines = git_stats['lines_added']
        
        if generated_lines == 0:
            return {'adoption_rate': 0, 'method': 'no_data'}
        
        if committed_lines == 0:
            # 没有Git数据，使用启发式方法
            return {
                'adoption_rate': 70,  # 默认估算
                'method': 'heuristic',
                'confidence': 'low'
            }
        
        # 基于Git提交的采纳率计算
        raw_rate = min(100, (committed_lines / generated_lines) * 100)
        
        # 调整因子（考虑到不是所有代码都通过Cursor生成）
        adjustment_factor = 0.8  # 假设80%的提交代码来自Cursor
        adjusted_rate = min(100, raw_rate / adjustment_factor)
        
        confidence = 'high' if git_stats['total_commits'] > 5 else 'medium'
        
        return {
            'adoption_rate': round(adjusted_rate, 1),
            'raw_rate': round(raw_rate, 1),
            'method': 'git_based',
            'confidence': confidence,
            'generated_lines': generated_lines,
            'committed_lines': committed_lines
        }
    
    def _get_daily_breakdown(self) -> Dict:
        """获取每日使用情况分解"""
        daily_stats = {}
        
        # 这里可以添加更详细的每日分析逻辑
        for i in range(self.days_back):
            date = (self.end_date - timedelta(days=i)).strftime('%Y-%m-%d')
            daily_stats[date] = {
                'conversations': 0,
                'generated_lines': 0,
                'estimated_adopted_lines': 0
            }
        
        return daily_stats
    
    def _analyze_sqlite_file(self, file_path: Path) -> Dict:
        """分析SQLite文件"""
        stats = {'conversations': 0, 'generated_lines': 0, 'code_blocks': 0}
        
        try:
            conn = sqlite3.connect(str(file_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            for table_name, in tables:
                try:
                    cursor.execute(f"SELECT * FROM `{table_name}`")
                    rows = cursor.fetchall()
                    
                    cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    for row in rows:
                        row_dict = dict(zip(columns, row))
                        if self._contains_chat_data(row_dict):
                            stats['conversations'] += 1
                            lines, blocks = self._count_code_in_text(str(row_dict))
                            stats['generated_lines'] += lines
                            stats['code_blocks'] += blocks
                            
                except Exception:
                    continue
            
            conn.close()
        except Exception as e:
            print(f"无法读取SQLite文件 {file_path}: {e}")
        
        return stats
    
    def _contains_chat_data(self, data: Dict) -> bool:
        """检查数据是否包含聊天信息"""
        chat_keywords = ['message', 'content', 'text', 'conversation', 'chat', 'response']
        data_str = str(data).lower()
        return any(keyword in data_str for keyword in chat_keywords)
    
    def _count_code_in_text(self, text: str) -> Tuple[int, int]:
        """统计文本中的代码行数和代码块数量"""
        if not isinstance(text, str):
            return 0, 0
        
        code_blocks = re.findall(r'```[\s\S]*?```', text, re.MULTILINE)
        block_count = len(code_blocks)
        
        total_lines = 0
        for block in code_blocks:
            lines = block.split('\n')
            code_lines = [line for line in lines[1:-1] if line.strip()]
            total_lines += len(code_lines)
        
        inline_code = re.findall(r'`[^`\n]+`', text)
        total_lines += len(inline_code)
        
        return total_lines, block_count
    
    def print_enhanced_report(self, stats: Dict):
        """打印增强版报告"""
        print("\n" + "="*70)
        print("🚀 Cursor IDE 增强版使用情况分析报告")
        print("="*70)
        
        print(f"\n📅 分析周期: 最近{self.days_back}天")
        print(f"💬 总对话数: {stats['total_conversations']}")
        print(f"📝 代码块数量: {stats['total_code_blocks']}")
        print(f"📊 生成代码行数: {stats['total_generated_lines']}")
        
        # Git分析结果
        git_stats = stats.get('git_analysis', {})
        if git_stats.get('total_commits', 0) > 0:
            print(f"\n🔄 Git提交分析:")
            print(f"   📦 总提交数: {git_stats['total_commits']}")
            print(f"   ➕ 新增代码行: {git_stats['lines_added']}")
            print(f"   ➖ 删除代码行: {git_stats['lines_deleted']}")
            print(f"   📁 变更文件数: {git_stats['files_changed']}")
        
        # 采纳率分析
        adoption = stats.get('adoption_analysis', {})
        if adoption:
            print(f"\n📈 代码采纳率分析:")
            print(f"   🎯 采纳率: {adoption['adoption_rate']}%")
            print(f"   🔍 分析方法: {adoption['method']}")
            print(f"   🎪 置信度: {adoption['confidence']}")
            
            if 'raw_rate' in adoption:
                print(f"   📊 原始比率: {adoption['raw_rate']}%")
        
        # 效率指标
        if stats['total_conversations'] > 0:
            avg_lines_per_conversation = stats['total_generated_lines'] / stats['total_conversations']
            print(f"\n⚡ 效率指标:")
            print(f"   📏 平均每次对话生成代码: {avg_lines_per_conversation:.1f}行")
            
            if git_stats.get('total_commits', 0) > 0:
                avg_lines_per_commit = git_stats['lines_added'] / git_stats['total_commits']
                print(f"   💾 平均每次提交代码: {avg_lines_per_commit:.1f}行")

def main():
    parser = argparse.ArgumentParser(description='增强版Cursor IDE使用情况分析')
    parser.add_argument('--days', type=int, default=7, help='分析最近几天的数据 (默认: 7)')
    args = parser.parse_args()
    
    analyzer = EnhancedCursorAnalyzer(days_back=args.days)
    stats = analyzer.analyze_with_git_context()
    analyzer.print_enhanced_report(stats)
    
    # 保存增强版报告
    output_file = f"enhanced_cursor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 增强版报告已保存到: {output_file}")

if __name__ == "__main__":
    main()
