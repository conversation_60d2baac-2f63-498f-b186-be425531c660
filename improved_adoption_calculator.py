#!/usr/bin/env python3
"""
改进的Cursor代码采纳率计算器
使用多种方法来更准确地估算代码采纳率
"""

import os
import json
import sqlite3
import re
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import platform
from typing import Dict, List, Tuple
import argparse

class ImprovedAdoptionCalculator:
    def __init__(self, days_back: int = 7):
        self.system = platform.system()
        self.cursor_dir = self._find_cursor_directory()
        self.days_back = days_back
        self.start_date = datetime.now() - timedelta(days=days_back)
        self.end_date = datetime.now()
        
    def _find_cursor_directory(self) -> Path:
        """查找Cursor的数据目录"""
        if self.system == "Darwin":  # macOS
            base_dir = Path.home() / "Library" / "Application Support" / "Cursor"
        elif self.system == "Windows":
            base_dir = Path(os.environ.get("APPDATA", "")) / "Cursor"
        else:  # Linux
            base_dir = Path.home() / ".config" / "Cursor"
        
        possible_paths = [base_dir / "User", base_dir]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        return Path(".")
    
    def calculate_multiple_adoption_rates(self) -> Dict:
        """使用多种方法计算采纳率"""
        print("🔍 开始多维度采纳率分析...")
        
        # 方法1: 基于文件变更时间的分析
        file_based_rate = self._calculate_file_based_adoption()
        
        # 方法2: 基于代码相似性的分析
        similarity_based_rate = self._calculate_similarity_based_adoption()
        
        # 方法3: 基于Git diff的智能分析
        git_intelligent_rate = self._calculate_intelligent_git_adoption()
        
        # 方法4: 基于会话上下文的分析
        context_based_rate = self._calculate_context_based_adoption()
        
        # 综合评估
        final_rate = self._synthesize_adoption_rates([
            file_based_rate,
            similarity_based_rate, 
            git_intelligent_rate,
            context_based_rate
        ])
        
        return {
            'file_based': file_based_rate,
            'similarity_based': similarity_based_rate,
            'git_intelligent': git_intelligent_rate,
            'context_based': context_based_rate,
            'final_estimate': final_rate,
            'analysis_methods': 4
        }
    
    def _calculate_file_based_adoption(self) -> Dict:
        """基于文件修改时间的采纳率分析"""
        print("📁 分析文件修改时间...")
        
        try:
            # 获取项目中最近修改的文件
            recent_files = []
            for root, dirs, files in os.walk("."):
                # 跳过隐藏目录和常见的非代码目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv']]
                
                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h')):
                        file_path = Path(root) / file
                        try:
                            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                            if self.start_date <= mtime <= self.end_date:
                                recent_files.append({
                                    'path': file_path,
                                    'mtime': mtime,
                                    'size': file_path.stat().st_size
                                })
                        except:
                            continue
            
            if not recent_files:
                return {'adoption_rate': 0, 'confidence': 'low', 'method': 'file_based', 'files_analyzed': 0}
            
            # 分析这些文件的代码量
            total_lines_in_modified_files = 0
            for file_info in recent_files:
                try:
                    with open(file_info['path'], 'r', encoding='utf-8') as f:
                        lines = len([line for line in f if line.strip()])
                        total_lines_in_modified_files += lines
                except:
                    continue
            
            # 获取Cursor生成的代码量
            cursor_stats = self._get_cursor_stats()
            generated_lines = cursor_stats.get('total_generated_lines', 0)
            
            if generated_lines == 0:
                adoption_rate = 0
            else:
                # 假设修改的文件中有一定比例来自Cursor
                estimated_cursor_lines_in_files = min(total_lines_in_modified_files, generated_lines * 0.6)
                adoption_rate = (estimated_cursor_lines_in_files / generated_lines) * 100
            
            return {
                'adoption_rate': round(adoption_rate, 1),
                'confidence': 'medium',
                'method': 'file_based',
                'files_analyzed': len(recent_files),
                'total_lines_in_files': total_lines_in_modified_files,
                'generated_lines': generated_lines
            }
            
        except Exception as e:
            print(f"文件分析出错: {e}")
            return {'adoption_rate': 0, 'confidence': 'low', 'method': 'file_based', 'error': str(e)}
    
    def _calculate_similarity_based_adoption(self) -> Dict:
        """基于代码相似性的采纳率分析"""
        print("🔍 分析代码相似性...")
        
        try:
            # 获取Cursor生成的代码片段
            cursor_code_blocks = self._extract_cursor_code_blocks()
            
            if not cursor_code_blocks:
                return {'adoption_rate': 0, 'confidence': 'low', 'method': 'similarity_based'}
            
            # 获取项目中的代码
            project_code = self._get_project_code()
            
            # 计算相似性
            similar_blocks = 0
            total_blocks = len(cursor_code_blocks)
            
            for cursor_block in cursor_code_blocks:
                if self._is_code_similar_to_project(cursor_block, project_code):
                    similar_blocks += 1
            
            adoption_rate = (similar_blocks / total_blocks) * 100 if total_blocks > 0 else 0
            
            return {
                'adoption_rate': round(adoption_rate, 1),
                'confidence': 'high' if total_blocks > 10 else 'medium',
                'method': 'similarity_based',
                'total_blocks_analyzed': total_blocks,
                'similar_blocks_found': similar_blocks
            }
            
        except Exception as e:
            print(f"相似性分析出错: {e}")
            return {'adoption_rate': 0, 'confidence': 'low', 'method': 'similarity_based', 'error': str(e)}
    
    def _calculate_intelligent_git_adoption(self) -> Dict:
        """智能Git分析，考虑时间窗口和代码修改"""
        print("🔄 智能Git分析...")
        
        try:
            # 获取更详细的Git统计
            since_date = self.start_date.strftime('%Y-%m-%d')
            
            # 获取详细的提交信息
            cmd = f"git log --since='{since_date}' --pretty=format:'%H|%ad|%s' --date=iso --numstat"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                return {'adoption_rate': 0, 'confidence': 'low', 'method': 'git_intelligent', 'error': 'No git data'}
            
            # 解析提交数据
            commits_data = self._parse_detailed_git_log(result.stdout)
            
            # 计算在Cursor使用时间窗口内的代码变更
            cursor_timeframe_changes = 0
            total_changes = 0
            
            for commit in commits_data:
                commit_time = datetime.fromisoformat(commit['date'].replace('Z', '+00:00').replace(' +', '+'))
                total_changes += commit['lines_added'] + commit['lines_deleted']
                
                # 如果提交时间在Cursor活跃期间，更可能包含Cursor生成的代码
                if self.start_date <= commit_time <= self.end_date:
                    cursor_timeframe_changes += commit['lines_added'] + commit['lines_deleted']
            
            # 获取Cursor统计
            cursor_stats = self._get_cursor_stats()
            generated_lines = cursor_stats.get('total_generated_lines', 0)
            
            if generated_lines == 0:
                adoption_rate = 0
            else:
                # 更智能的计算：考虑时间窗口内的变更
                estimated_adoption = min(100, (cursor_timeframe_changes / generated_lines) * 100 * 1.5)  # 1.5倍调整因子
                adoption_rate = estimated_adoption
            
            return {
                'adoption_rate': round(adoption_rate, 1),
                'confidence': 'high' if len(commits_data) > 3 else 'medium',
                'method': 'git_intelligent',
                'commits_analyzed': len(commits_data),
                'timeframe_changes': cursor_timeframe_changes,
                'total_changes': total_changes,
                'generated_lines': generated_lines
            }
            
        except Exception as e:
            print(f"智能Git分析出错: {e}")
            return {'adoption_rate': 0, 'confidence': 'low', 'method': 'git_intelligent', 'error': str(e)}
    
    def _calculate_context_based_adoption(self) -> Dict:
        """基于会话上下文的采纳率分析"""
        print("💬 分析会话上下文...")
        
        try:
            # 分析Cursor会话中的采纳指示词
            cursor_conversations = self._get_cursor_conversations()
            
            total_code_blocks = 0
            adopted_indicators = 0
            
            for conversation in cursor_conversations:
                content = str(conversation).lower()
                
                # 统计代码块
                code_blocks = len(re.findall(r'```[\s\S]*?```', str(conversation)))
                total_code_blocks += code_blocks
                
                # 查找采纳指示词
                adoption_keywords = [
                    'thanks', 'thank you', 'works', 'perfect', 'great', 'good',
                    'applied', 'used', 'implemented', 'added', 'copied',
                    '谢谢', '感谢', '好的', '可以', '采用', '应用', '实现'
                ]
                
                rejection_keywords = [
                    'error', 'wrong', 'doesn\'t work', 'failed', 'issue', 'problem',
                    '错误', '不对', '有问题', '失败'
                ]
                
                adoption_score = sum(1 for keyword in adoption_keywords if keyword in content)
                rejection_score = sum(1 for keyword in rejection_keywords if keyword in content)
                
                if adoption_score > rejection_score and code_blocks > 0:
                    adopted_indicators += code_blocks
            
            adoption_rate = (adopted_indicators / total_code_blocks) * 100 if total_code_blocks > 0 else 0
            
            return {
                'adoption_rate': round(adoption_rate, 1),
                'confidence': 'medium',
                'method': 'context_based',
                'total_code_blocks': total_code_blocks,
                'adopted_indicators': adopted_indicators,
                'conversations_analyzed': len(cursor_conversations)
            }
            
        except Exception as e:
            print(f"上下文分析出错: {e}")
            return {'adoption_rate': 0, 'confidence': 'low', 'method': 'context_based', 'error': str(e)}
    
    def _synthesize_adoption_rates(self, rates: List[Dict]) -> Dict:
        """综合多种方法的结果"""
        valid_rates = [r for r in rates if r.get('adoption_rate', 0) > 0]
        
        if not valid_rates:
            return {'adoption_rate': 0, 'confidence': 'low', 'method': 'synthesized'}
        
        # 根据置信度加权平均
        weighted_sum = 0
        weight_sum = 0
        
        confidence_weights = {'high': 3, 'medium': 2, 'low': 1}
        
        for rate in valid_rates:
            weight = confidence_weights.get(rate.get('confidence', 'low'), 1)
            weighted_sum += rate['adoption_rate'] * weight
            weight_sum += weight
        
        final_rate = weighted_sum / weight_sum if weight_sum > 0 else 0
        
        # 确定最终置信度
        high_confidence_count = sum(1 for r in valid_rates if r.get('confidence') == 'high')
        if high_confidence_count >= 2:
            final_confidence = 'high'
        elif len(valid_rates) >= 3:
            final_confidence = 'medium'
        else:
            final_confidence = 'low'
        
        return {
            'adoption_rate': round(final_rate, 1),
            'confidence': final_confidence,
            'method': 'synthesized',
            'methods_used': len(valid_rates),
            'individual_rates': [r['adoption_rate'] for r in valid_rates]
        }
    
    def _get_cursor_stats(self) -> Dict:
        """获取Cursor基础统计"""
        # 这里复用之前的逻辑
        workspace_storage = self.cursor_dir / "workspaceStorage"
        total_generated_lines = 0
        total_conversations = 0
        
        if workspace_storage.exists():
            for workspace in workspace_storage.iterdir():
                if workspace.is_dir():
                    state_file = workspace / "state.vscdb"
                    if state_file.exists():
                        stats = self._analyze_sqlite_file(state_file)
                        total_generated_lines += stats.get('generated_lines', 0)
                        total_conversations += stats.get('conversations', 0)
        
        return {
            'total_generated_lines': total_generated_lines,
            'total_conversations': total_conversations
        }
    
    def _analyze_sqlite_file(self, file_path: Path) -> Dict:
        """分析SQLite文件"""
        stats = {'conversations': 0, 'generated_lines': 0}
        
        try:
            conn = sqlite3.connect(str(file_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            for table_name, in tables:
                try:
                    cursor.execute(f"SELECT * FROM `{table_name}`")
                    rows = cursor.fetchall()
                    
                    for row in rows:
                        row_str = str(row)
                        if any(keyword in row_str.lower() for keyword in ['message', 'content', 'text']):
                            stats['conversations'] += 1
                            lines, _ = self._count_code_in_text(row_str)
                            stats['generated_lines'] += lines
                            
                except Exception:
                    continue
            
            conn.close()
        except Exception:
            pass
        
        return stats
    
    def _count_code_in_text(self, text: str) -> Tuple[int, int]:
        """统计文本中的代码行数"""
        if not isinstance(text, str):
            return 0, 0
        
        code_blocks = re.findall(r'```[\s\S]*?```', text, re.MULTILINE)
        block_count = len(code_blocks)
        
        total_lines = 0
        for block in code_blocks:
            lines = block.split('\n')
            code_lines = [line for line in lines[1:-1] if line.strip()]
            total_lines += len(code_lines)
        
        return total_lines, block_count
    
    def _extract_cursor_code_blocks(self) -> List[str]:
        """提取Cursor生成的代码块"""
        # 简化实现
        return []
    
    def _get_project_code(self) -> str:
        """获取项目代码"""
        # 简化实现
        return ""
    
    def _is_code_similar_to_project(self, cursor_block: str, project_code: str) -> bool:
        """检查代码相似性"""
        # 简化实现
        return False
    
    def _parse_detailed_git_log(self, log_output: str) -> List[Dict]:
        """解析详细的Git日志"""
        commits = []
        lines = log_output.strip().split('\n')
        current_commit = None
        
        for line in lines:
            if '|' in line and len(line.split('|')) == 3:
                if current_commit:
                    commits.append(current_commit)
                
                hash_val, date, message = line.split('|')
                current_commit = {
                    'hash': hash_val,
                    'date': date,
                    'message': message,
                    'lines_added': 0,
                    'lines_deleted': 0
                }
            elif line.strip() and current_commit:
                parts = line.split('\t')
                if len(parts) >= 3:
                    added = int(parts[0]) if parts[0].isdigit() else 0
                    deleted = int(parts[1]) if parts[1].isdigit() else 0
                    current_commit['lines_added'] += added
                    current_commit['lines_deleted'] += deleted
        
        if current_commit:
            commits.append(current_commit)
        
        return commits
    
    def _get_cursor_conversations(self) -> List[Dict]:
        """获取Cursor对话记录"""
        # 简化实现
        return []
    
    def print_detailed_report(self, results: Dict):
        """打印详细的采纳率分析报告"""
        print("\n" + "🎯" * 30)
        print("📊 改进的Cursor代码采纳率分析报告")
        print("🎯" * 30)
        
        print(f"\n📅 分析周期: 最近{self.days_back}天")
        
        # 显示各种方法的结果
        methods = ['file_based', 'similarity_based', 'git_intelligent', 'context_based']
        method_names = {
            'file_based': '📁 文件修改时间分析',
            'similarity_based': '🔍 代码相似性分析', 
            'git_intelligent': '🔄 智能Git分析',
            'context_based': '💬 会话上下文分析'
        }
        
        print(f"\n📈 各方法分析结果:")
        for method in methods:
            if method in results:
                data = results[method]
                rate = data.get('adoption_rate', 0)
                confidence = data.get('confidence', 'unknown')
                print(f"   {method_names[method]}: {rate}% (置信度: {confidence})")
        
        # 最终综合结果
        final = results.get('final_estimate', {})
        print(f"\n🎯 综合采纳率估算:")
        print(f"   最终采纳率: {final.get('adoption_rate', 0)}%")
        print(f"   综合置信度: {final.get('confidence', 'unknown')}")
        print(f"   使用方法数: {final.get('methods_used', 0)}")
        
        if 'individual_rates' in final:
            rates = final['individual_rates']
            print(f"   各方法结果: {rates}")
            print(f"   结果范围: {min(rates):.1f}% - {max(rates):.1f}%")

def main():
    parser = argparse.ArgumentParser(description='改进的Cursor采纳率计算器')
    parser.add_argument('--days', type=int, default=7, help='分析最近几天的数据 (默认: 7)')
    args = parser.parse_args()
    
    calculator = ImprovedAdoptionCalculator(days_back=args.days)
    results = calculator.calculate_multiple_adoption_rates()
    calculator.print_detailed_report(results)
    
    # 保存结果
    output_file = f"improved_adoption_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细分析结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
