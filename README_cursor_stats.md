# Cursor IDE 使用情况统计工具

这个工具可以帮你分析Cursor IDE的使用情况，统计代码生成和采纳情况。

## 功能特性

- 🔍 自动查找Cursor的数据目录
- 📊 统计代码生成行数
- 📈 估算代码采纳率
- 💬 统计对话次数
- 📅 支持自定义时间范围
- 📄 生成详细的JSON报告

## 使用方法

### 基本使用

```bash
# 分析最近7天的使用情况
python cursor_usage_analyzer.py

# 分析最近14天的使用情况
python cursor_usage_analyzer.py --days 14
```

### 输出示例

```
🤖 Cursor IDE 使用情况统计报告
============================================================

📅 分析周期: 2024-01-15 至 2024-01-22 (7天)
📁 分析文件数: 5
💬 总对话数: 23
📝 代码块数量: 45
📊 生成代码行数: 1,234
✅ 估算采纳行数: 926
📈 估算采纳率: 75%

📋 文件详情:
----------------------------------------
workspace1.json: 12次对话, 567行代码
workspace2.db: 8次对话, 345行代码
state.vscdb: 3次对话, 322行代码
```

## 数据来源

工具会自动查找以下位置的Cursor数据：

### macOS
- `~/Library/Application Support/Cursor/User/workspaceStorage/`
- `~/Library/Application Support/Cursor/logs/`

### Windows
- `%APPDATA%\Cursor\User\workspaceStorage\`
- `%APPDATA%\Cursor\logs\`

### Linux
- `~/.config/Cursor/User/workspaceStorage/`
- `~/.config/Cursor/logs/`

## 支持的文件格式

- ✅ JSON文件 (`.json`)
- ✅ SQLite数据库 (`.db`, `.sqlite`, `.vscdb`)
- ✅ 日志文件 (`.log`)

## 统计说明

### 代码行数统计
- 统计所有代码块中的有效代码行
- 包括```代码块和`单行代码`
- 排除空行和注释行

### 采纳率估算
由于无法直接获取实际采纳的代码，工具使用以下方法估算：
- 默认采纳率: 75%
- 基于代码块的复杂度和对话上下文进行调整

### 对话统计
- 统计包含代码生成的有效对话
- 排除纯文本对话和系统消息

## 注意事项

1. **权限要求**: 需要读取Cursor的数据目录
2. **数据准确性**: 统计结果基于可访问的日志文件，可能不包含所有数据
3. **采纳率**: 采纳率为估算值，实际采纳情况可能有差异
4. **隐私**: 工具只读取统计信息，不会上传或泄露代码内容

## 故障排除

### 找不到Cursor目录
```bash
# 手动指定Cursor目录
export CURSOR_DIR="/path/to/cursor/data"
python cursor_usage_analyzer.py
```

### 没有找到数据文件
- 确保Cursor已经使用过一段时间
- 检查Cursor的设置中是否启用了历史记录
- 尝试使用更长的时间范围 `--days 30`

### 统计结果为0
- 检查时间范围是否正确
- 确认在指定时间内确实使用了Cursor
- 查看生成的JSON报告文件获取更多详情

## 输出文件

工具会生成一个详细的JSON报告文件：
- 文件名格式: `cursor_usage_report_YYYYMMDD_HHMMSS.json`
- 包含完整的统计数据和文件分析详情
- 可用于进一步的数据分析

## 依赖要求

- Python 3.6+
- 标准库模块（无需额外安装）

## 许可证

MIT License
