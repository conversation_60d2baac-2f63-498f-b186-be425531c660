#!/usr/bin/env python3
"""
现实的Cursor代码采纳率计算器
基于更合理的假设和多种指标来估算采纳率
"""

import os
import json
import sqlite3
import re
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import platform
from typing import Dict, List, Tuple
import argparse

class RealisticAdoptionCalculator:
    def __init__(self, days_back: int = 7):
        self.system = platform.system()
        self.cursor_dir = self._find_cursor_directory()
        self.days_back = days_back
        self.start_date = datetime.now() - timedelta(days=days_back)
        self.end_date = datetime.now()
        
    def _find_cursor_directory(self) -> Path:
        """查找Cursor的数据目录"""
        if self.system == "Darwin":  # macOS
            base_dir = Path.home() / "Library" / "Application Support" / "Cursor"
        elif self.system == "Windows":
            base_dir = Path(os.environ.get("APPDATA", "")) / "Cursor"
        else:  # Linux
            base_dir = Path.home() / ".config" / "Cursor"
        
        possible_paths = [base_dir / "User", base_dir]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        return Path(".")
    
    def calculate_realistic_adoption_rate(self) -> Dict:
        """计算更现实的采纳率"""
        print("🔍 开始现实采纳率分析...")
        
        # 获取基础数据
        cursor_stats = self._get_cursor_stats()
        git_stats = self._get_git_stats()
        file_stats = self._get_file_modification_stats()
        
        # 计算多种采纳率估算
        estimates = []
        
        # 方法1: 基于文件活动的估算
        file_based_estimate = self._estimate_from_file_activity(cursor_stats, file_stats)
        if file_based_estimate['confidence'] > 0:
            estimates.append(file_based_estimate)
        
        # 方法2: 基于Git活动的调整估算
        git_based_estimate = self._estimate_from_git_activity(cursor_stats, git_stats)
        if git_based_estimate['confidence'] > 0:
            estimates.append(git_based_estimate)
        
        # 方法3: 基于使用模式的估算
        pattern_based_estimate = self._estimate_from_usage_patterns(cursor_stats)
        if pattern_based_estimate['confidence'] > 0:
            estimates.append(pattern_based_estimate)
        
        # 方法4: 基于行业经验的估算
        experience_based_estimate = self._estimate_from_experience(cursor_stats, git_stats)
        estimates.append(experience_based_estimate)  # 总是包含这个作为基准
        
        # 综合所有估算
        final_estimate = self._synthesize_estimates(estimates)
        
        return {
            'cursor_stats': cursor_stats,
            'git_stats': git_stats,
            'file_stats': file_stats,
            'estimates': estimates,
            'final_estimate': final_estimate,
            'analysis_date': datetime.now().isoformat()
        }
    
    def _get_cursor_stats(self) -> Dict:
        """获取Cursor使用统计"""
        workspace_storage = self.cursor_dir / "workspaceStorage"
        stats = {
            'total_conversations': 0,
            'total_generated_lines': 0,
            'total_code_blocks': 0,
            'avg_lines_per_conversation': 0,
            'avg_lines_per_block': 0
        }
        
        if workspace_storage.exists():
            for workspace in workspace_storage.iterdir():
                if workspace.is_dir():
                    state_file = workspace / "state.vscdb"
                    if state_file.exists():
                        file_stats = self._analyze_sqlite_file(state_file)
                        stats['total_conversations'] += file_stats.get('conversations', 0)
                        stats['total_generated_lines'] += file_stats.get('generated_lines', 0)
                        stats['total_code_blocks'] += file_stats.get('code_blocks', 0)
        
        # 计算平均值
        if stats['total_conversations'] > 0:
            stats['avg_lines_per_conversation'] = stats['total_generated_lines'] / stats['total_conversations']
        
        if stats['total_code_blocks'] > 0:
            stats['avg_lines_per_block'] = stats['total_generated_lines'] / stats['total_code_blocks']
        
        return stats
    
    def _get_git_stats(self) -> Dict:
        """获取Git统计信息"""
        stats = {
            'total_commits': 0,
            'lines_added': 0,
            'lines_deleted': 0,
            'files_changed': 0,
            'avg_lines_per_commit': 0,
            'has_git': False
        }
        
        try:
            if not (Path.cwd() / ".git").exists():
                return stats
            
            stats['has_git'] = True
            since_date = self.start_date.strftime('%Y-%m-%d')
            
            # 获取提交统计
            cmd = f"git log --since='{since_date}' --pretty=format:'%H' --numstat"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                current_commit = None
                
                for line in lines:
                    if len(line) == 40 and all(c in '0123456789abcdef' for c in line):
                        # 这是一个commit hash
                        if current_commit is not None:
                            stats['total_commits'] += 1
                        current_commit = line
                    elif line.strip() and current_commit:
                        # 这是文件变更统计
                        parts = line.split('\t')
                        if len(parts) >= 3:
                            added = int(parts[0]) if parts[0].isdigit() else 0
                            deleted = int(parts[1]) if parts[1].isdigit() else 0
                            stats['lines_added'] += added
                            stats['lines_deleted'] += deleted
                            stats['files_changed'] += 1
                
                if current_commit is not None:
                    stats['total_commits'] += 1
            
            # 计算平均值
            if stats['total_commits'] > 0:
                stats['avg_lines_per_commit'] = (stats['lines_added'] + stats['lines_deleted']) / stats['total_commits']
                
        except Exception as e:
            print(f"Git分析出错: {e}")
        
        return stats
    
    def _get_file_modification_stats(self) -> Dict:
        """获取文件修改统计"""
        stats = {
            'modified_files': 0,
            'total_lines_in_modified_files': 0,
            'avg_file_size': 0
        }
        
        try:
            for root, dirs, files in os.walk("."):
                # 跳过隐藏目录和常见的非代码目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv', 'dist', 'build']]
                
                for file in files:
                    if file.endswith(('.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h', '.go', '.rs')):
                        file_path = Path(root) / file
                        try:
                            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                            if self.start_date <= mtime <= self.end_date:
                                stats['modified_files'] += 1
                                
                                # 计算文件行数
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    lines = len([line for line in f if line.strip()])
                                    stats['total_lines_in_modified_files'] += lines
                        except:
                            continue
            
            if stats['modified_files'] > 0:
                stats['avg_file_size'] = stats['total_lines_in_modified_files'] / stats['modified_files']
                
        except Exception as e:
            print(f"文件分析出错: {e}")
        
        return stats
    
    def _estimate_from_file_activity(self, cursor_stats: Dict, file_stats: Dict) -> Dict:
        """基于文件活动估算采纳率"""
        if cursor_stats['total_generated_lines'] == 0 or file_stats['modified_files'] == 0:
            return {'adoption_rate': 0, 'confidence': 0, 'method': 'file_activity'}
        
        # 假设修改的文件中有一定比例来自Cursor
        # 这个比例基于文件修改的活跃程度
        cursor_contribution_ratio = min(0.8, cursor_stats['total_conversations'] / (file_stats['modified_files'] * 5))
        
        estimated_cursor_lines = file_stats['total_lines_in_modified_files'] * cursor_contribution_ratio
        adoption_rate = min(100, (estimated_cursor_lines / cursor_stats['total_generated_lines']) * 100)
        
        # 置信度基于数据质量
        confidence = min(100, (file_stats['modified_files'] * 10 + cursor_stats['total_conversations']) / 2)
        
        return {
            'adoption_rate': round(adoption_rate, 1),
            'confidence': round(confidence, 1),
            'method': 'file_activity',
            'reasoning': f"基于{file_stats['modified_files']}个修改文件和{cursor_stats['total_conversations']}次对话"
        }
    
    def _estimate_from_git_activity(self, cursor_stats: Dict, git_stats: Dict) -> Dict:
        """基于Git活动估算采纳率"""
        if not git_stats['has_git'] or cursor_stats['total_generated_lines'] == 0:
            return {'adoption_rate': 0, 'confidence': 0, 'method': 'git_activity'}
        
        total_git_changes = git_stats['lines_added'] + git_stats['lines_deleted']
        
        if total_git_changes == 0:
            return {'adoption_rate': 0, 'confidence': 0, 'method': 'git_activity'}
        
        # 更合理的计算：考虑Cursor可能贡献的比例
        # 假设Cursor贡献了30-70%的代码变更（基于使用频率）
        usage_intensity = min(1.0, cursor_stats['total_conversations'] / 50)  # 标准化使用强度
        cursor_contribution_ratio = 0.3 + (usage_intensity * 0.4)  # 30%-70%
        
        estimated_cursor_contribution = total_git_changes * cursor_contribution_ratio
        adoption_rate = min(100, (estimated_cursor_contribution / cursor_stats['total_generated_lines']) * 100)
        
        # 置信度基于Git活动和Cursor使用的匹配程度
        confidence = min(100, (git_stats['total_commits'] * 15 + cursor_stats['total_conversations']) / 3)
        
        return {
            'adoption_rate': round(adoption_rate, 1),
            'confidence': round(confidence, 1),
            'method': 'git_activity',
            'reasoning': f"基于{git_stats['total_commits']}次提交和{total_git_changes}行变更，估算Cursor贡献{cursor_contribution_ratio:.1%}"
        }
    
    def _estimate_from_usage_patterns(self, cursor_stats: Dict) -> Dict:
        """基于使用模式估算采纳率"""
        if cursor_stats['total_conversations'] == 0:
            return {'adoption_rate': 0, 'confidence': 0, 'method': 'usage_patterns'}
        
        # 基于使用模式的启发式估算
        avg_lines_per_conversation = cursor_stats['avg_lines_per_conversation']
        
        # 根据每次对话的代码量推断采纳率
        if avg_lines_per_conversation > 20:
            # 大量代码生成，可能采纳率较低（需要更多修改）
            base_adoption_rate = 45
        elif avg_lines_per_conversation > 10:
            # 中等代码生成，采纳率中等
            base_adoption_rate = 65
        elif avg_lines_per_conversation > 5:
            # 少量代码生成，采纳率较高
            base_adoption_rate = 80
        else:
            # 很少代码生成，可能主要是咨询
            base_adoption_rate = 30
        
        # 根据使用频率调整
        daily_conversations = cursor_stats['total_conversations'] / self.days_back
        if daily_conversations > 20:
            # 高频使用，可能采纳率更高
            frequency_bonus = 10
        elif daily_conversations > 10:
            frequency_bonus = 5
        else:
            frequency_bonus = 0
        
        adoption_rate = min(100, base_adoption_rate + frequency_bonus)
        confidence = 60  # 中等置信度，因为这是基于模式的推断
        
        return {
            'adoption_rate': round(adoption_rate, 1),
            'confidence': confidence,
            'method': 'usage_patterns',
            'reasoning': f"基于平均每次对话{avg_lines_per_conversation:.1f}行代码和每日{daily_conversations:.1f}次对话的使用模式"
        }
    
    def _estimate_from_experience(self, cursor_stats: Dict, git_stats: Dict) -> Dict:
        """基于行业经验估算采纳率"""
        # 基于AI编程助手的一般采纳率经验数据
        # 研究表明，AI生成代码的采纳率通常在40-80%之间
        
        base_adoption_rate = 60  # 基准采纳率
        
        # 根据项目活跃度调整
        if git_stats['has_git'] and git_stats['total_commits'] > 10:
            # 活跃项目，采纳率可能更高
            activity_bonus = 10
        elif git_stats['has_git'] and git_stats['total_commits'] > 5:
            activity_bonus = 5
        else:
            activity_bonus = 0
        
        # 根据使用强度调整
        if cursor_stats['total_conversations'] > 100:
            # 重度使用，说明用户信任度高
            usage_bonus = 15
        elif cursor_stats['total_conversations'] > 50:
            usage_bonus = 10
        elif cursor_stats['total_conversations'] > 20:
            usage_bonus = 5
        else:
            usage_bonus = 0
        
        adoption_rate = min(100, base_adoption_rate + activity_bonus + usage_bonus)
        confidence = 75  # 较高置信度，基于行业经验
        
        return {
            'adoption_rate': round(adoption_rate, 1),
            'confidence': confidence,
            'method': 'industry_experience',
            'reasoning': f"基于行业经验基准{base_adoption_rate}%，项目活跃度调整+{activity_bonus}%，使用强度调整+{usage_bonus}%"
        }
    
    def _synthesize_estimates(self, estimates: List[Dict]) -> Dict:
        """综合多个估算结果"""
        if not estimates:
            return {'adoption_rate': 0, 'confidence': 0, 'method': 'none'}
        
        # 按置信度加权平均
        weighted_sum = 0
        weight_sum = 0
        
        for estimate in estimates:
            weight = estimate['confidence'] / 100  # 将置信度转换为权重
            weighted_sum += estimate['adoption_rate'] * weight
            weight_sum += weight
        
        if weight_sum == 0:
            final_rate = sum(e['adoption_rate'] for e in estimates) / len(estimates)
            final_confidence = sum(e['confidence'] for e in estimates) / len(estimates)
        else:
            final_rate = weighted_sum / weight_sum
            final_confidence = sum(e['confidence'] for e in estimates) / len(estimates)
        
        return {
            'adoption_rate': round(final_rate, 1),
            'confidence': round(final_confidence, 1),
            'method': 'weighted_synthesis',
            'estimates_used': len(estimates),
            'rate_range': [min(e['adoption_rate'] for e in estimates), max(e['adoption_rate'] for e in estimates)]
        }
    
    def _analyze_sqlite_file(self, file_path: Path) -> Dict:
        """分析SQLite文件"""
        stats = {'conversations': 0, 'generated_lines': 0, 'code_blocks': 0}
        
        try:
            conn = sqlite3.connect(str(file_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            for table_name, in tables:
                try:
                    cursor.execute(f"SELECT * FROM `{table_name}`")
                    rows = cursor.fetchall()
                    
                    for row in rows:
                        row_str = str(row)
                        if any(keyword in row_str.lower() for keyword in ['message', 'content', 'text']):
                            stats['conversations'] += 1
                            lines, blocks = self._count_code_in_text(row_str)
                            stats['generated_lines'] += lines
                            stats['code_blocks'] += blocks
                            
                except Exception:
                    continue
            
            conn.close()
        except Exception:
            pass
        
        return stats
    
    def _count_code_in_text(self, text: str) -> Tuple[int, int]:
        """统计文本中的代码行数和代码块数"""
        if not isinstance(text, str):
            return 0, 0
        
        code_blocks = re.findall(r'```[\s\S]*?```', text, re.MULTILINE)
        block_count = len(code_blocks)
        
        total_lines = 0
        for block in code_blocks:
            lines = block.split('\n')
            code_lines = [line for line in lines[1:-1] if line.strip()]
            total_lines += len(code_lines)
        
        return total_lines, block_count
    
    def print_detailed_report(self, results: Dict):
        """打印详细报告"""
        print("\n" + "🎯" * 35)
        print("📊 现实的Cursor代码采纳率分析报告")
        print("🎯" * 35)
        
        cursor_stats = results['cursor_stats']
        git_stats = results['git_stats']
        file_stats = results['file_stats']
        
        print(f"\n📅 分析周期: 最近{self.days_back}天")
        
        print(f"\n💬 Cursor使用统计:")
        print(f"   总对话数: {cursor_stats['total_conversations']}")
        print(f"   生成代码行数: {cursor_stats['total_generated_lines']:,}")
        print(f"   代码块数量: {cursor_stats['total_code_blocks']}")
        print(f"   平均每次对话: {cursor_stats['avg_lines_per_conversation']:.1f}行")
        
        if git_stats['has_git']:
            print(f"\n🔄 Git活动统计:")
            print(f"   提交次数: {git_stats['total_commits']}")
            print(f"   新增行数: {git_stats['lines_added']:,}")
            print(f"   删除行数: {git_stats['lines_deleted']:,}")
            print(f"   平均每次提交: {git_stats['avg_lines_per_commit']:.1f}行")
        
        print(f"\n📁 文件修改统计:")
        print(f"   修改文件数: {file_stats['modified_files']}")
        print(f"   修改文件总行数: {file_stats['total_lines_in_modified_files']:,}")
        
        print(f"\n📈 采纳率估算结果:")
        for estimate in results['estimates']:
            method_names = {
                'file_activity': '📁 文件活动分析',
                'git_activity': '🔄 Git活动分析',
                'usage_patterns': '📊 使用模式分析',
                'industry_experience': '🏭 行业经验分析'
            }
            method_name = method_names.get(estimate['method'], estimate['method'])
            print(f"   {method_name}: {estimate['adoption_rate']}% (置信度: {estimate['confidence']:.0f}%)")
            print(f"      理由: {estimate['reasoning']}")
        
        final = results['final_estimate']
        print(f"\n🎯 综合采纳率估算:")
        print(f"   最终采纳率: {final['adoption_rate']}%")
        print(f"   综合置信度: {final['confidence']:.0f}%")
        print(f"   估算范围: {final['rate_range'][0]:.1f}% - {final['rate_range'][1]:.1f}%")
        print(f"   使用方法数: {final['estimates_used']}")
        
        # 估算实际采纳的代码量
        estimated_adopted_lines = int(cursor_stats['total_generated_lines'] * final['adoption_rate'] / 100)
        print(f"\n✅ 估算采纳代码量: {estimated_adopted_lines:,}行")
        
        # 效率评估
        if cursor_stats['total_conversations'] > 0:
            efficiency = estimated_adopted_lines / cursor_stats['total_conversations']
            print(f"📈 平均每次对话有效代码: {efficiency:.1f}行")

def main():
    parser = argparse.ArgumentParser(description='现实的Cursor采纳率计算器')
    parser.add_argument('--days', type=int, default=7, help='分析最近几天的数据 (默认: 7)')
    args = parser.parse_args()
    
    calculator = RealisticAdoptionCalculator(days_back=args.days)
    results = calculator.calculate_realistic_adoption_rate()
    calculator.print_detailed_report(results)
    
    # 保存结果
    output_file = f"realistic_adoption_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细分析结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
