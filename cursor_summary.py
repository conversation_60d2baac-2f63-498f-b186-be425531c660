#!/usr/bin/env python3
"""
Cursor使用情况总结脚本
生成美观的使用情况总结报告
"""

import json
from datetime import datetime
from pathlib import Path
import argparse

def generate_summary_report():
    """生成总结报告"""
    
    # 查找最新的报告文件
    report_files = list(Path(".").glob("*cursor_report_*.json"))
    if not report_files:
        print("❌ 未找到分析报告文件，请先运行分析脚本")
        return
    
    # 使用最新的报告文件
    latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
    
    with open(latest_report, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print_beautiful_summary(data, latest_report.name)

def print_beautiful_summary(data: dict, filename: str):
    """打印美观的总结报告"""
    
    print("\n" + "🎯" * 25)
    print("📊 CURSOR IDE 使用情况总结报告")
    print("🎯" * 25)
    
    # 基本统计
    print(f"\n📅 统计周期: {data.get('analysis_period', {}).get('start', 'N/A')} 至 {data.get('analysis_period', {}).get('end', 'N/A')}")
    
    conversations = data.get('total_conversations', 0)
    generated_lines = data.get('total_generated_lines', 0)
    code_blocks = data.get('total_code_blocks', 0)
    
    print(f"\n💬 对话统计:")
    print(f"   总对话次数: {conversations}")
    print(f"   平均每天对话: {conversations / 7:.1f} 次")
    
    print(f"\n📝 代码生成统计:")
    print(f"   总生成代码行数: {generated_lines:,}")
    print(f"   总代码块数量: {code_blocks}")
    print(f"   平均每个代码块: {generated_lines / code_blocks:.1f} 行" if code_blocks > 0 else "   平均每个代码块: 0 行")
    print(f"   平均每次对话生成: {generated_lines / conversations:.1f} 行" if conversations > 0 else "   平均每次对话生成: 0 行")
    
    # Git分析（如果有的话）
    git_analysis = data.get('git_analysis')
    if git_analysis and git_analysis.get('total_commits', 0) > 0:
        print(f"\n🔄 Git提交分析:")
        print(f"   提交次数: {git_analysis['total_commits']}")
        print(f"   新增代码行: {git_analysis['lines_added']:,}")
        print(f"   删除代码行: {git_analysis['lines_deleted']:,}")
        print(f"   净增加行数: {git_analysis['lines_added'] - git_analysis['lines_deleted']:,}")
        print(f"   变更文件数: {git_analysis['files_changed']}")
    
    # 采纳率分析
    adoption_analysis = data.get('adoption_analysis')
    if adoption_analysis:
        adoption_rate = adoption_analysis.get('adoption_rate', 0)
        method = adoption_analysis.get('method', 'unknown')
        confidence = adoption_analysis.get('confidence', 'unknown')
        
        print(f"\n📈 代码采纳率:")
        print(f"   采纳率: {adoption_rate}%")
        print(f"   分析方法: {method}")
        print(f"   置信度: {confidence}")
        
        if method == 'git_based':
            print(f"   (基于Git提交记录的真实采纳率)")
        else:
            print(f"   (基于启发式算法的估算采纳率)")
    else:
        # 使用基础估算
        estimated_rate = data.get('estimated_adoption_rate', 75)
        estimated_lines = data.get('estimated_adopted_lines', 0)
        print(f"\n📈 代码采纳率 (估算):")
        print(f"   估算采纳率: {estimated_rate}%")
        print(f"   估算采纳行数: {estimated_lines:,}")
    
    # 效率评估
    print(f"\n⚡ 效率评估:")
    if conversations > 0:
        efficiency_score = min(100, (generated_lines / conversations) * 2)  # 简单的效率评分
        print(f"   编程效率评分: {efficiency_score:.1f}/100")
        
        if efficiency_score >= 80:
            print(f"   评级: 🌟🌟🌟🌟🌟 极高效率")
        elif efficiency_score >= 60:
            print(f"   评级: 🌟🌟🌟🌟 高效率")
        elif efficiency_score >= 40:
            print(f"   评级: 🌟🌟🌟 中等效率")
        elif efficiency_score >= 20:
            print(f"   评级: 🌟🌟 较低效率")
        else:
            print(f"   评级: 🌟 低效率")
    
    # 使用建议
    print(f"\n💡 使用建议:")
    
    if conversations < 10:
        print(f"   📢 建议增加与Cursor的互动频率，充分利用AI辅助编程")
    
    if generated_lines / conversations < 5 if conversations > 0 else True:
        print(f"   📝 建议在对话中要求更详细的代码示例")
    
    if adoption_analysis and adoption_analysis.get('adoption_rate', 0) < 30:
        print(f"   🎯 代码采纳率较低，建议:")
        print(f"      - 提供更具体的需求描述")
        print(f"      - 分步骤请求代码修改")
        print(f"      - 及时反馈代码质量问题")
    
    if git_analysis and git_analysis.get('total_commits', 0) > 20:
        print(f"   🚀 提交频率很高，说明开发很活跃！")
    
    # 时间趋势（如果有每日数据）
    daily_stats = data.get('daily_stats', {})
    if daily_stats:
        print(f"\n📈 每日使用趋势:")
        sorted_days = sorted(daily_stats.items())
        for date, stats in sorted_days[-3:]:  # 显示最近3天
            print(f"   {date}: {stats.get('conversations', 0)}次对话, {stats.get('generated_lines', 0)}行代码")
    
    print(f"\n📄 详细数据文件: {filename}")
    print(f"🕐 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n" + "🎯" * 25)

def main():
    parser = argparse.ArgumentParser(description='生成Cursor使用情况总结报告')
    args = parser.parse_args()
    
    generate_summary_report()

if __name__ == "__main__":
    main()
